# openai
OPENAI_API_BASE=https://api.openai.com/v1
OPENAI_API_KEY=YOUR_API_KEY
OPENAI_MODEL=gemini-2.5-flash
OPENAI_REASONING_EFFORT=medium

# google
GOOGLE_API_BASE=https://generativelanguage.googleapis.com/v1beta
GOOGLE_API_KEY=YOUR_API_KEY
GOOGLE_MODEL=gemini-2.5-flash
GOOGLE_THINKING_BUDGET=-1 # -1表示动态思考

# 阿里百炼
BAILIAN_APP_BASE=https://dashscope.aliyuncs.com/api/v1/apps
BAILIAN_APP_ID=YOUR_APP_ID
BAILIAN_API_BASE=https://dashscope.aliyuncs.com/compatible-mode/v1
BAILIAN_API_KEY=YOUR_API_KEY
BAILIAN_MODEL=qwen3-14b
QWEN3_ENABLE_THINKING=false

# 字节扣子
COZE_APP_BASE=https://api.coze.cn/v1
COZE_API_KEY=YOUR_API_KEY
COZE_WORKFLOW_ID=YOUR_WORKFLOW_ID

# 字节火山
VOLCANO_API_BASE=https://ark.cn-beijing.volces.com/api/v3
VOLCANO_API_KEY=YOUR_API_KEY
VOLCANO_MODEL=doubao-1-5-pro-32k-250115
DOUBAO_SEED_ENABLE_THINKING=false

# 火山向量数据库
VOLCANO_KNOWLEDGE_API_BASE=https://api-knowledgebase.mlp.cn-beijing.volces.com
VOLCANO_KNOWLEDGE_API_AK=YOUR_API_AK
VOLCANO_KNOWLEDGE_API_SK=YOUR_API_SK
VOLCANO_KNOWLEDGE_COLLECTION_NAME=YOUR_COLLECTION_NAME
VOLCANO_KNOWLEDGE_PROJECT=default

# rerank
RERANK_API_BASE=http://*************:9090/v1
RERANK_API_KEY=YOUR_API_KEY
RERANK_MODEL=bge-reranker-v2-m3

# 其他配置
FASTAPI_HOST=0.0.0.0
FASTAPI_PORT=8000

# 日志配置
APP_LOG_LEVEL=INFO
APP_LOG_DIR=./logs
APP_LOG_ROTATE_WHEN=midnight
APP_LOG_BACKUP_COUNT=7
APP_LOG_FILE_BASENAME=ai_kefu