# AI Agent - 游戏客服 FAQ 筛选器

> **路径说明**：本文档中的路径均使用基于项目根目录的绝对路径格式 `<PROJECT_ROOT>/...`，其中 `<PROJECT_ROOT>` 表示项目根目录。

## 概述

本模块实现了一个基于FastAPI的AI聊天网页Demo项目的核心组件，作为多AI平台(百炼、Coze、火山方舟)的API代理，实现了完整的FAQ筛选器Agent。核心包含查询重写→问题分类→向量召回→答案检索→重排序的完整AI工作流，使用Python 3.11+uv包管理，支持模块化LLM实现和Docker部署。

该模块设计为可被上层服务（如 `<PROJECT_ROOT>/backend_host/src/ai_app/server/`）集成使用，同时也支持独立的测试和开发。支持多渠道FAQ数据管理，具备分类失败时的向量召回补充机制。

## 核心工作流

1.  **输入**: 接收玩家与客服的对话历史 (`conversation`) 以及一些额外的上下文信息 (`context`，例如渠道、平台）。
2.  **查询重写 (Query Rewrite)**: 使用专门的LLM，结合对话历史和上下文，将用户的原始问题重写为一个清晰、独立且包含必要背景信息的查询语句 (`query_rewrite`)。
3.  **FAQ 目录提取**: 从 `<PROJECT_ROOT>/backend_host/resources/faq_data/faq.xlsx` 文件中解析 FAQ 数据，通过统一的FAQ仓库管理器自动加载所有sheet数据，并提取出 Markdown 格式的目录结构，其中包含类别键 (`category_key`) 和类别描述 (`category_desc`)。支持多渠道FAQ数据（第一张表为通用数据，后续表为渠道增量数据）。
4.  **问题分类 (Classification)**: 使用LLM，根据重写后的查询 (`query_rewrite`) 和 FAQ 目录结构，判断查询最符合哪个具体的 FAQ 类别。LLM 被要求输出一至多行文本，每行包含类别键路径 (`category_key_path`，例如 `1.1.2.`、`1.2.` 或 `0`) 。
5.  **向量召回 (Retrieval Fallback)**: 当分类结果为空或仅包含 `category_key_path` 为 "0" 的项目时，自动启用向量数据库召回机制，使用火山向量数据库进行语义检索，最多召回3条相关结果作为补充。
6.  **答案检索**: 根据分类或召回得到的 `category_key_path`，通过FAQ仓库管理器在 `<PROJECT_ROOT>/backend_host/resources/faq_data/faq.xlsx` 文件中查找并提取对应的最终答案 (`answer`) 和问题示例 (`question_example`)。
7.  **答案重排序（可选）**: 使用专门的重排序模型，根据玩家问题 (`query_rewrite`) 和检索到的答案，对答案进行重排序，以确保答案的准确性和相关性。
8.  **输出**: 返回检索到的答案列表，包含答案内容、分类链、得分和推理过程。如果分类失败或找不到答案，则返回相应的保底话术。

## 主要组件

### 核心模块

*   **`agent.py` (`FAQFilterAgent`)**: Agent 的主入口和协调器，负责编排整个工作流程，包括分类失败时的向量召回补充机制。
*   **`data_parser.py` (`FAQDataParser`)**: 负责通过FAQ仓库管理器加载和解析FAQ数据，提供获取 Markdown 目录结构、根据 `category_key_path` 查询答案、以及双向路径转换的功能。现在主要使用Excel格式数据文件，支持多渠道数据自动合并。
*   **`llm_clients.py`**:
    *   `QueryRewriteClient`: 封装与执行查询重写任务的LLM的API交互。
    *   `FAQClassifierClient`: 封装与执行问题分类任务的LLM的API交互。
    *   `FAQRetrieveClient`: 封装向量数据库检索功能，支持语义召回补充。
    *   `FAQRerankClient`: 封装与执行答案重排序任务的LLM的API交互。

### LLM实现层

*   **`<PROJECT_ROOT>/backend_host/src/ai_app/agents/shared/llm_impl/`**: 多平台LLM实现（共享组件）
    *   `volcano_impl.py`: 火山引擎LLM和向量数据库实现
    *   `bailian_impl.py`: 百炼平台LLM实现
    *   `rerank_impl.py`: 重排序模型实现

### 数据和配置

*   **`<PROJECT_ROOT>/backend_host/src/ai_app/agents/shared/prompts/`**: 存放用于指导 LLM 的 Prompt 模板文件（共享组件）。
    *   `common/rewrite_prompt.md`: 查询重写任务的 Prompt。
    *   `faq_filter/classify_prompt.md`: 问题分类任务的 Prompt。
*   **`<PROJECT_ROOT>/backend_host/resources/faq_data/`**: FAQ数据文件。
    *   `faq.xlsx`: Excel格式FAQ数据，支持多sheet（通用数据表+渠道增量表），通过FAQ仓库管理器自动加载和管理。
    *   `backups/`: 数据修改时的自动备份目录。

### 测试模块

*   **`<PROJECT_ROOT>/backend_host/tests/agents/`**: 测试文件（统一测试目录）
    *   `test_llm_clients.py`: LLM客户端测试，支持查询重写、分类、召回等功能测试。
    *   `faq_filter_agent/test_faq_management.py`: 数据解析器测试。

## 配置

系统通过 `<PROJECT_ROOT>/backend_host/src/ai_app/config.py` 进行配置管理，支持以下配置项：

### LLM平台配置
*   **百炼平台**: `bailian_api_key`, `bailian_api_base`, `bailian_model`
*   **火山引擎**: `volcano_api_key`, `volcano_api_base`, `volcano_model`
*   **Coze平台**: `coze_api_key`, `coze_app_base`, `coze_workflow_id`

### 向量数据库配置
*   **火山知识库**: `volcano_knowledge_api_base`, `volcano_knowledge_api_ak`, `volcano_knowledge_api_sk`
*   **集合配置**: `volcano_knowledge_collection_name`, `volcano_knowledge_project`

### 重排序配置
*   **重排序API**: `rerank_api_base`, `rerank_api_key`, `rerank_model`

### 文件路径配置
*   **FAQ数据**: `faq_excel_path`
*   **Prompt模板**: `rewrite_prompt_path`, `classify_prompt_path`

配置可通过环境变量或配置文件设置，支持多渠道FAQ文件自动切换。

## 使用

### 基本使用

上层服务（如 `<PROJECT_ROOT>/backend_host/src/ai_app/server/`）可以通过导入 `FAQFilterAgent` 类并调用其处理方法来使用此模块：

```python
from ai_app.agents.faq_filter_agent.agent import FAQFilterAgent
from ai_app.models.chat import ChatRequest

# 初始化Agent
agent = FAQFilterAgent(
    context_params={'channel_name': 'zulong'},
    model_platform='volcano'
)

# 处理用户请求
response = await agent.process_user_request(chat_request)
```

### 数据管理

FAQ数据现在通过统一的仓库管理器（`FAQRepoManager`）自动加载和管理：

- **Excel文件**: `resources/faq_data/faq.xlsx`
  - 第一张表：通用数据表
  - 后续表：各渠道增量表（以sheet名称作为渠道名）
- **自动加载**: 应用启动时自动加载所有sheet数据到内存
- **多渠道支持**: 自动处理不同渠道的FAQ数据合并和切换
- **备份机制**: 数据修改时自动备份到 `backups/` 目录
- **数据验证**: 自动验证数据完整性和格式正确性

#### 支持的数据格式

**Excel格式**（7列）：
1. 分类层级1-5
2. 答案内容
3. 问题示例

**JSON格式**：
```json
[
    {
        "category_desc": "账号",
        "sub_category": [...],
        "candidates": [
            {
                "answer": "专员，您好。请问您能更具体的描述您的账号遇到的问题吗？",
                "question_example": "账号问题找谁问",
                "key_path": "1.10"
            }
        ],
        "key_path": "1."
    },
    ...
]
```

## 测试

### 单元测试

使用 `<PROJECT_ROOT>/backend_host/tests/agents/test_llm_clients.py` 进行各组件测试。

从项目根目录执行：
```bash
cd <PROJECT_ROOT>/backend_host

# 测试查询重写
python -m tests.agents.faq_filter_agent.test_llm_clients rewrite \
  --query "我密码忘了" --model-platform volcano

# 测试问题分类
python -m tests.agents.faq_filter_agent.test_llm_clients classify \
  --query "我忘记密码了" --faq-structure-file src/ai_app/agents/faq_filter_agent/data/faq_doc.json

# 测试向量召回
python -m tests.agents.faq_filter_agent.test_llm_clients retrieve \
  --query "我忘记密码了" --top-n 5
```

### 功能特性

✅ **多平台LLM支持**: 百炼、火山引擎、Coze
✅ **向量召回补充**: 分类失败时自动启用语义检索
✅ **多渠道FAQ**: 支持不同渠道的FAQ数据文件
✅ **双向数据转换**: Excel ↔ JSON 格式转换
✅ **完整工作流**: 重写→分类→召回→检索→重排序
✅ **错误处理**: 优雅降级和详细日志记录
✅ **Token统计**: 完整的模型使用量统计

## 架构设计

### 分层架构

```
┌─────────────────────────────────────────┐
│              FAQFilterAgent             │  ← 主协调器
├─────────────────────────────────────────┤
│  QueryRewrite │ Classifier │ Retrieve   │  ← 业务逻辑层
│  Client       │ Client     │ Client     │
├─────────────────────────────────────────┤
│  VolcanoLLM   │ BailianLLM │ RerankImpl │  ← LLM实现层
│  Impl         │ Impl       │            │
├─────────────────────────────────────────┤
│  FAQDataParser │ ExcelConverter         │  ← 数据处理层
└─────────────────────────────────────────┘
```

### 数据流

```
用户查询 → 查询重写 → 问题分类 ┬→ 答案检索 → 重排序 → 返回结果
                           ↓
                      分类失败检测
                           ↓
                      向量召回补充
```

## 性能优化

### 召回策略
- **主路径**: LLM分类，精确匹配FAQ结构
- **补充路径**: 向量召回，语义相似度匹配
- **触发条件**: 分类为空或仅返回category_key_path="0"
- **召回限制**: 最多5条结果，避免噪声

### 缓存机制
- FAQ数据解析结果缓存
- 向量检索结果可缓存（未实现）
- LLM响应缓存（未实现）

## 错误处理

### 分级处理
1. **致命错误**: 配置缺失、文件不存在 → 抛出异常
2. **业务错误**: LLM调用失败 → 返回错误响应
3. **降级处理**: 召回失败 → 继续使用分类结果
4. **保底机制**: 无答案时返回保底话术

### 日志记录
- **INFO**: 正常流程和关键节点
- **WARNING**: 非致命错误和降级处理
- **ERROR**: 业务错误和异常情况
- **DEBUG**: 详细的调试信息

## 扩展指南

### 添加新的LLM平台
1. 在 `<PROJECT_ROOT>/backend_host/src/ai_app/agents/shared/llm_impl/` 下创建新的实现类
2. 继承相应的接口（`BaseLLMImpl`, `EmbeddingImpl`, `RerankImpl`）
3. 在 `<PROJECT_ROOT>/backend_host/src/ai_app/config.py` 中添加配置项
4. 在当前目录的 `agent.py` 中添加初始化逻辑

### 添加新的数据源
1. 默认数据源位于`<PROJECT_ROOT>/backend_host/resources/faq_data/faq.xlsx`
2. 新增增量表可在上述excel文件中添加新表，并以渠道channel的名称给sheet命名
3. 或者使用其它的excel表，需配置`<PROJECT_ROOT>/backend_host/.env`中的`FAQ_EXCEL_PATH`

### 自定义召回策略
1. 扩展 `FAQRetrieveClient` 的触发条件
2. 调整召回结果的数量和过滤逻辑
3. 实现自定义的相似度计算