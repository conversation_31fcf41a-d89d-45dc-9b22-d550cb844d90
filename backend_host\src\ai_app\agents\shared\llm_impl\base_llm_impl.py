import abc
import re
import json
import httpx
from typing import List, Dict, Any, Tuple, Optional

from ai_app.config import config

from ai_app.models.chat import ChatModelUsage # 保持对通用模型的引用
from ai_app.shared.exceptions import LLMAPIError, LLMResponseError
from ai_app.shared.request_tracing import get_traced_logger

logger = get_traced_logger(__name__)

# Default timeout for HTTP requests
DEFAULT_TIMEOUT = 60.0

class BaseLLMImpl(abc.ABC):
    """与 LLM 服务交互的抽象基类。"""
    
    def __init__(self, api_key: str, api_base: str, model_id: str):
        """初始化 LLM 客户端的通用参数。
        
        Args:
            api_key: API 密钥，用于身份验证。
            api_base: API 基础 URL。
            model_id: 模型 ID。
            
        Raises:
            ValueError: 如果任何必需参数为空。
        """
        if not api_key or not api_base or not model_id:
            raise ValueError("API Key, API Base, and Model ID cannot be empty.")
            
        self.api_key = api_key
        self.api_base = api_base.rstrip('/')
        self.model_id = model_id
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
            "Accept": "application/json",
        }

    async def chat_completion(
        self,
        messages: List[Dict[str, str]],
        timeout: float = DEFAULT_TIMEOUT,
        temperature: Optional[float] = None,
        top_p: Optional[float] = None,
        max_tokens: Optional[int] = None,
        response_format: Optional[Dict[str, str]] = None # For JSON mode
    ) -> Tuple[str, ChatModelUsage, Dict[str, Any]]:
        """
        与 LLM 进行聊天补全交互的核心方法。
        
        提供OpenAI兼容格式的默认实现，子类可以重写此方法以适配特定平台的API。
        此默认实现要求子类设置以下属性：
        - self.api_base: API基础URL
        - self.headers: 请求头字典
        - self.model_id: 模型ID

        Args:
            messages: 对话消息列表，格式遵循 OpenAI 风格 [{ "role": "user/system/assistant", "content": "..." }]。
            timeout: 请求超时时间 (秒)。
            temperature: 控制生成文本的随机性。
            top_p: 控制核心采样的概率阈值。
            max_tokens: 生成响应的最大 token 数量。
            response_format: 指定响应格式，例如 {"type": "json_object"}。

        Returns:
            Tuple[str, ChatModelUsage, Dict[str, Any]]: 一个包含以下内容的元组:
                - 响应消息的内容 (str)
                - 包含模型ID和token使用量的 ChatModelUsage 对象
                - 原始的API响应字典 (Dict[str, Any])

        Raises:
            LLMAPIError: 如果 API 调用失败 (网络错误, 超时, 服务端错误等)。
            LLMResponseError: 如果 API 响应格式不正确或无法解析。
        """
        # 检查子类是否设置了必要的属性
        if not hasattr(self, 'api_base') or not hasattr(self, 'headers') or not hasattr(self, 'model_id'):
            raise NotImplementedError(
                "子类必须设置 api_base, headers 和 model_id 属性，或者重写 chat_completion 方法"
            )
        
        # 构建OpenAI兼容的请求体
        api_url = f"{self.api_base.rstrip('/')}/chat/completions"
        request_body = {
            "model": self.model_id,
            "messages": messages,
            "stream": False,
        }
        
        # 添加可选参数
        if temperature is not None:
            # chatgpt的O系列模型不支持temperature参数
            if any(x in self.model_id for x in ['o1', 'o3', 'o4']):
                logger.warning(f"Model {self.model_id} does not support temperature parameter. Ignoring temperature value.")
            else:
                request_body["temperature"] = temperature
        if top_p is not None:
            request_body["top_p"] = top_p
        if max_tokens is not None:
            request_body["max_tokens"] = max_tokens
        if response_format is not None:
            request_body["response_format"] = response_format

        if config.openai_reasoning_effort in ['low', 'medium', 'high']:
            request_body["reasoning_effort"] = config.openai_reasoning_effort
        
        logger.debug(f"Sending request to {api_url} with body: {json.dumps(request_body, indent=2, ensure_ascii=False)}")
        
        # 发送HTTP请求
        async with httpx.AsyncClient() as client:
            try:
                response = await client.post(
                    api_url,
                    json=request_body,
                    headers=self.headers,
                    timeout=timeout
                )
                response.raise_for_status()
                
            except httpx.TimeoutException as e:
                logger.error(f"Request timeout after {timeout}s to {api_url}")
                raise LLMAPIError(f"Request timeout after {timeout}s") from e
            except httpx.HTTPStatusError as e:
                error_detail = "Unknown API error"
                try:
                    error_json = e.response.json()
                    if 'error' in error_json:
                        error_detail = error_json['error'].get('message', str(error_json['error']))
                    elif 'message' in error_json:
                        error_detail = error_json['message']
                except (json.JSONDecodeError, KeyError):
                    error_detail = e.response.text
                
                logger.error(f"API returned status {e.response.status_code} from {api_url}: {error_detail}")
                raise LLMAPIError(f"API returned status {e.response.status_code}: {error_detail}") from e
            except httpx.RequestError as e:
                logger.error(f"Network error when calling {api_url}: {e}")
                raise LLMAPIError(f"Network error: {e}") from e
        
        # 解析响应
        try:
            response_data = response.json()
            logger.debug(f"Raw API response: {json.dumps(response_data, indent=2, ensure_ascii=False)}")
            
            # 检查API错误
            if 'error' in response_data and response_data['error']:
                error_info = response_data['error']
                error_message = error_info.get('message', json.dumps(error_info))
                logger.error(f"API returned error in response body: {error_message}")
                raise LLMAPIError(f"API returned error: {error_message}")
            
            # 验证响应结构
            if not response_data.get('choices') or not response_data['choices']:
                logger.error(f"Unexpected response structure: Missing or empty 'choices'. Response: {response_data}")
                raise LLMResponseError("Unexpected API response structure: Missing or empty choices.")
            
            choice = response_data['choices'][0]
            if not choice.get('message') or 'content' not in choice['message']:
                logger.error(f"Unexpected response structure: Missing 'message.content'. Response: {response_data}")
                raise LLMResponseError("Unexpected API response structure: Missing message content.")
            
            content = choice['message']['content']
            
            # 构建使用量信息
            usage_data = response_data.get('usage', {})
            if not usage_data or 'prompt_tokens' not in usage_data or 'completion_tokens' not in usage_data:
                logger.warning(f"Usage information missing or incomplete in API response. Response: {response_data}")
                usage = ChatModelUsage(
                    model_id=response_data.get('model', self.model_id),
                    input_tokens=usage_data.get('prompt_tokens', 0),
                    output_tokens=usage_data.get('completion_tokens', 0)
                )
            else:
                usage = ChatModelUsage(
                    model_id=response_data.get('model', self.model_id),
                    input_tokens=usage_data['prompt_tokens'],
                    output_tokens=usage_data['completion_tokens']
                )
            
            # 清理内容（移除可能的包装标签）
            cleaned_content = self.clean_content(content)
            
            return cleaned_content, usage, response_data
            
        except json.JSONDecodeError as e:
            logger.error(f"Failed to decode JSON response from API: {response.text}")
            raise LLMResponseError(f"Failed to decode API JSON response: {e}") from e
        except (KeyError, IndexError, TypeError) as e:
            logger.error(f"Failed to parse expected data from API response. Response: {response_data}. Error: {e}", exc_info=True)
            raise LLMResponseError(f"Unexpected API response structure: {e}") from e
    
    def get_thinking_content(self, response: Dict[str, Any]) -> Optional[str]:
        """
        从 LLM 返回的内容中提取思考过程。
        """
        if 'choices' in response and len(response['choices']) > 0:
            choice = response['choices'][0] # 获取第一个choice
            if 'message' in choice:
                message = choice['message']
                if 'reasoning_content' in message:
                    reasoning_content = message['reasoning_content']
                    if isinstance(reasoning_content, str) and len(reasoning_content) > 0:
                        return reasoning_content
            if 'content' in message:
                content = message['content']
                if isinstance(content, str) and len(content) > 0:
                    thinking_content, _ = self.extract_think_label(content)
                    if thinking_content is not None:
                        return thinking_content
        return None
    
    def extract_think_label(self, content: str) -> Tuple[Optional[str], str]:
        """
        有的think内容会用<think>标签包裹，有的不会
        这里判断是否用<think>标签包裹，并返回包裹的内容和清理后的内容
        """
        think_label_pattern = r'<think>(.*?)</think>(.*)'
        # 使用 re.DOTALL 标志，使得 `.` 可以匹配包括换行符在内的任意字符
        match = re.search(think_label_pattern, content, re.DOTALL)
        if match:
            # 提取并清理思考过程和剩余内容
            thinking_content = match.group(1).strip()
            remaining_content = match.group(2).strip()
            return thinking_content, remaining_content
        return None, content
    
    def clean_content(self, content: str) -> str:
        """
        从 LLM 返回的内容中移除可能的包裹标签，包括：
        - <think> 和 </think> 标签
        - markdown JSON 包裹: ```json 和 ```
        - XML 节点标签: <xxx> 和 </xxx>
        - 混合使用的各种标签组合
        
        处理逻辑：
        1. 去除前后空白字符（包括空格、制表符、换行符等）
        2. 如果内嵌了think标签，先拿掉它们
        3. 迭代地从开头和结尾移除各种包裹标签
        4. 直到没有更多标签可以移除为止
        """
        # 使用更彻底的空白字符清理方法
        # 不仅移除空格，还移除制表符(\t)、换行符(\n)、回车符(\r)、换页符(\f)、垂直制表符(\v)等
        content = content.strip(' \t\n\r\f\v')

        # 先移除think标签
        _, content = self.extract_think_label(content)
        
        # 定义需要移除的标签模式
        # 开头标签模式：```json, ```, <任意标签>
        start_patterns = [
            r'^```json\s*',  # ```json 开头
            r'^```\s*',      # ``` 开头  
            r'^<[^>]+>\s*'   # XML 开头标签，如 <xxx>
        ]
        
        # 结尾标签模式：```, </任意标签>
        end_patterns = [
            r'\s*```$',      # ``` 结尾
            r'\s*</[^>]+>$'  # XML 结尾标签，如 </xxx>
        ]
        
        # 迭代移除标签，直到没有更多标签可以移除
        previous_content = ""
        while previous_content != content:
            previous_content = content
            
            # 移除开头的标签
            for pattern in start_patterns:
                match = re.match(pattern, content)
                if match:
                    content = content[match.end():]
                    logger.debug(f"Removed start wrapper: {match.group()}")
                    break
            
            # 移除结尾的标签
            for pattern in end_patterns:
                match = re.search(pattern, content)
                if match:
                    content = content[:match.start()]
                    logger.debug(f"Removed end wrapper: {match.group()}")
                    break
            
            # 再次彻底去除空白字符
            content = content.strip(' \t\n\r\f\v')
        
        return content