import httpx
import json
from typing import List, Dict, Any, Tuple, Optional

from ai_app.config import config
from ai_app.models.chat import ChatModelUsage
from ai_app.shared.exceptions import LLMAPIError, LLMResponseError
from ai_app.agents.shared.llm_impl.base_llm_impl import BaseLLMImpl, DEFAULT_TIMEOUT
from ai_app.shared.request_tracing import get_traced_logger

logger = get_traced_logger(__name__)

class GoogleLLMImpl(BaseLLMImpl):
    """与 Google Gemini 大模型服务交互的具体实现。"""

    def __init__(self, api_key: str, api_base: str, model_id: str):
        """初始化 Google Gemini 客户端。

        Args:
            api_key: Google Gemini API 密钥。
            api_base: Google Gemini API 基础 URL (例如: 'https://generativelanguage.googleapis.com/v1beta')。
            model_id: Gemini 模型 ID (例如: 'gemini-2.5-flash')。
        """
        # 调用基类构造函数进行通用初始化
        super().__init__(api_key, api_base, model_id)
        
        # 重写headers以适配Google API的认证方式
        self.headers = {
            "x-goog-api-key": self.api_key,
            "Content-Type": "application/json",
            "Accept": "application/json",
        }

    def _convert_messages_to_gemini_format(self, messages: List[Dict[str, str]]) -> List[Dict[str, Any]]:
        """将OpenAI格式的消息转换为Gemini API格式。
        
        Gemini API使用不同的消息格式:
        - role: 'user' 或 'model' (而不是 'assistant')
        - parts: [{'text': '...'}] (而不是直接的 'content')
        
        Args:
            messages: OpenAI格式的消息列表
            
        Returns:
            转换后的Gemini格式消息列表
        """
        gemini_messages = []
        
        for message in messages:
            role = message.get('role', 'user')
            content = message.get('content', '')
            
            # 转换role映射
            if role == 'assistant':
                gemini_role = 'model'
            elif role == 'system':
                # Gemini API通常将system消息作为第一个user消息处理
                gemini_role = 'user'
            else:
                gemini_role = role
            
            # 构建Gemini格式的消息
            gemini_message = {
                'role': gemini_role,
                'parts': [{'text': content}]
            }
            
            gemini_messages.append(gemini_message)
        
        return gemini_messages

    async def chat_completion(
        self,
        messages: List[Dict[str, str]],
        timeout: float = DEFAULT_TIMEOUT,
        temperature: Optional[float] = None,
        top_p: Optional[float] = None,
        max_tokens: Optional[int] = None,
        response_format: Optional[Dict[str, str]] = None
    ) -> Tuple[str, ChatModelUsage, Dict[str, Any]]:
        """调用 Google Gemini generateContent API。
        
        重写基类方法以适配Gemini API的特殊格式和端点。
        
        Args:
            messages: 对话消息列表，格式遵循 OpenAI 风格。
            timeout: 请求超时时间 (秒)。
            temperature: 控制生成文本的随机性 (0.0-2.0)。
            top_p: 控制核心采样的概率阈值 (0.0-1.0)。
            max_tokens: 生成响应的最大 token 数量。
            response_format: 指定响应格式，例如 {"type": "json_object"}。

        Returns:
            Tuple[str, ChatModelUsage, Dict[str, Any]]: 包含响应内容、使用量和原始响应的元组。

        Raises:
            LLMAPIError: 如果 API 调用失败。
            LLMResponseError: 如果 API 响应格式不正确或无法解析。
        """
        # 构建Gemini API的URL
        api_url = f"{self.api_base}/models/{self.model_id}:generateContent"
        
        # 转换消息格式
        gemini_contents = self._convert_messages_to_gemini_format(messages)
        
        # 构建请求体
        request_body = {
            "contents": gemini_contents,
        }
        
        # 添加生成配置
        generation_config = {}
        if temperature is not None:
            generation_config["temperature"] = temperature
        if top_p is not None:
            generation_config["topP"] = top_p
        if max_tokens is not None:
            generation_config["maxOutputTokens"] = max_tokens
        
        # 处理JSON响应格式
        if response_format and response_format.get('type') == 'json_object':
            generation_config["responseMimeType"] = "application/json"

        generation_config["thinkingConfig"] = {
            "thinkingBudget": config.google_thinking_budget,
            "includeThoughts": True,
        }
        
        if generation_config:
            request_body["generationConfig"] = generation_config
        
        logger.debug(f"Calling Google Gemini API: {api_url} with model {self.model_id}")
        logger.debug(f"Request Body: {json.dumps(request_body, ensure_ascii=False, indent=2)}")

        async with httpx.AsyncClient(timeout=timeout) as client:
            try:
                response = await client.post(
                    api_url,
                    headers=self.headers,
                    json=request_body
                )
                response.raise_for_status()
                
            except httpx.TimeoutException as e:
                logger.error(f"Google Gemini API request timed out to {api_url}: {e}")
                raise LLMAPIError(f"Request timed out after {timeout}s: {e}") from e
            except httpx.RequestError as e:
                logger.error(f"Google Gemini API request error to {api_url}: {e}")
                raise LLMAPIError(f"Request failed: {e}") from e
            except httpx.HTTPStatusError as e:
                error_detail = e.response.text
                try:
                    error_json = e.response.json()
                    if 'error' in error_json:
                        error_detail = error_json['error'].get('message', error_detail)
                except json.JSONDecodeError:
                    pass
                logger.error(f"Google Gemini API returned error status {e.response.status_code} from {api_url}: {error_detail}")
                raise LLMAPIError(f"API returned status {e.response.status_code}: {error_detail}") from e

        try:
            response_data = response.json()
            logger.debug(f"Raw Google Gemini API response: {json.dumps(response_data, indent=2, ensure_ascii=False)}")

            # 检查API错误
            if 'error' in response_data:
                error_info = response_data['error']
                error_message = error_info.get('message', json.dumps(error_info))
                logger.error(f"Google Gemini API returned error in response body: {error_message}")
                raise LLMAPIError(f"API returned error: {error_message}")

            # 验证响应结构 - Gemini API使用不同的结构
            if not response_data.get('candidates') or not response_data['candidates']:
                logger.error(f"Unexpected response structure from Google Gemini API. Missing or empty 'candidates'. Response: {response_data}")
                raise LLMResponseError("Unexpected API response structure: Missing or empty candidates.")

            candidate = response_data['candidates'][0]
            if not candidate.get('content') or not candidate['content'].get('parts'):
                logger.error(f"Unexpected response structure from Google Gemini API. Missing 'content.parts'. Response: {response_data}")
                raise LLMResponseError("Unexpected API response structure: Missing content parts.")

            # 提取文本内容
            parts = candidate['content']['parts']
            content_text = ""
            for part in parts:
                if 'text' in part and part.get('thought') is not True:
                    content_text += part['text']

            # 构建使用量信息 - Gemini API的使用量结构
            usage_data = response_data.get('usageMetadata', {})
            if not usage_data:
                logger.warning(f"Usage information missing in Google Gemini API response. Response: {response_data}")
                usage = ChatModelUsage(
                    model_id=self.model_id,
                    input_tokens=0,
                    output_tokens=0
                )
            else:
                usage = ChatModelUsage(
                    model_id=self.model_id,
                    input_tokens=usage_data.get('promptTokenCount', 0),
                    output_tokens=usage_data.get('candidatesTokenCount', 0)
                )

            # 清理内容
            cleaned_content = self.clean_content(content_text)

            return cleaned_content, usage, response_data

        except json.JSONDecodeError as e:
            logger.error(f"Failed to decode JSON response from Google Gemini API: {response.text}")
            raise LLMResponseError(f"Failed to decode API JSON response: {e}") from e
        except (KeyError, IndexError, TypeError) as e:
            logger.error(f"Failed to parse expected data from Google Gemini API response. Response: {response_data}. Error: {e}", exc_info=True)
            raise LLMResponseError(f"Unexpected API response structure: {e}") from e

    def get_thinking_content(self, response: Dict[str, Any]) -> Optional[str]:
        """从 Google Gemini 返回的内容中提取思考过程。
        
        根据实际的Gemini API响应结构，思考内容位于：
        candidates[0].content.parts中，当part有thought=true属性时，
        该part的text字段就是思考内容。
        
        Args:
            response: Google Gemini API的原始响应字典
            
        Returns:
            提取的思考内容字符串，如果没有找到则返回None
        """
        try:
            # 检查candidates中是否有思考内容
            if 'candidates' in response and response['candidates']:
                candidate = response['candidates'][0]
                
                # 检查content.parts中是否有thought=true的部分
                if 'content' in candidate and 'parts' in candidate['content']:
                    for part in candidate['content']['parts']:
                        # 当part有thought=true属性时，text字段就是思考内容
                        if part.get('thought') is True and 'text' in part:
                            thinking_text = part['text']
                            if isinstance(thinking_text, str) and thinking_text.strip():
                                return thinking_text.strip()
            
            logger.debug("No thinking content found in Google Gemini API response")
            return None
            
        except (KeyError, TypeError, AttributeError) as e:
            logger.warning(f"Error extracting thinking content from Google Gemini API response: {e}")
            return None